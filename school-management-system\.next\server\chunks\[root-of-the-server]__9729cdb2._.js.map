{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/api/admin/students/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { prisma } from '@/lib/db';\nimport { z } from 'zod';\nimport { hasPermission } from '@/lib/rbac';\nimport bcrypt from 'bcryptjs';\n\n// Validation schema for creating a student\nconst createStudentSchema = z.object({\n  firstName: z.string().min(1, 'First name is required'),\n  lastName: z.string().min(1, 'Last name is required'),\n  email: z.string().email('Invalid email address'),\n  dateOfBirth: z.string().regex(/^\\d{4}-\\d{2}-\\d{2}$/, 'Date must be in YYYY-MM-DD format'),\n  gender: z.enum(['MALE', 'FEMALE', 'OTHER']),\n  phoneNumber: z.string().optional(),\n  address: z.string().optional(),\n  classId: z.string().min(1, 'Class ID is required'),\n  parentName: z.string().optional(),\n  parentPhone: z.string().optional(),\n  rollNumber: z.string().optional(),\n});\n\n// GET /api/admin/students - List all students\nexport async function GET(request: NextRequest) {\n  try {\n    // Temporarily bypass authentication for testing\n    // const session = await getServerSession(authOptions);\n    // if (!session?.user) {\n    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    // }\n\n    // if (!hasPermission(session.user.role, 'students:read')) {\n    //   return NextResponse.json({ error: 'Forbidden' }, { status: 403 });\n    // }\n\n    // Parse query parameters\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get('page') || '1');\n    const limit = parseInt(searchParams.get('limit') || '10');\n    const search = searchParams.get('search') || '';\n    const classId = searchParams.get('classId') || '';\n    const gender = searchParams.get('gender') || '';\n\n    // Fetch all students with related data\n    const students = await prisma.student.findMany({\n      include: {\n        user: true,\n        currentClass: true,\n        currentSection: true,\n      },\n      orderBy: { createdAt: 'desc' },\n    });\n    \n    // Apply filters\n    let filteredStudents = students;\n    \n    if (search) {\n      filteredStudents = students.filter(student => \n        student.user?.firstName?.toLowerCase().includes(search.toLowerCase()) ||\n        student.user?.lastName?.toLowerCase().includes(search.toLowerCase()) ||\n        student.user?.email?.toLowerCase().includes(search.toLowerCase())\n      );\n    }\n\n    if (classId) {\n      filteredStudents = filteredStudents.filter(student => \n        student.currentClassId === parseInt(classId)\n      );\n    }\n\n    if (gender) {\n      filteredStudents = filteredStudents.filter(student => \n        student.gender === gender\n      );\n    }\n\n    // Apply pagination\n    const totalCount = filteredStudents.length;\n    const totalPages = Math.ceil(totalCount / limit);\n    const hasNextPage = page < totalPages;\n    const hasPrevPage = page > 1;\n    \n    const startIndex = (page - 1) * limit;\n    const endIndex = startIndex + limit;\n    const paginatedStudents = filteredStudents.slice(startIndex, endIndex);\n\n    return NextResponse.json({\n      students: paginatedStudents,\n      pagination: {\n        page,\n        limit,\n        totalCount,\n        totalPages,\n        hasNextPage,\n        hasPrevPage,\n      },\n    });\n  } catch (error) {\n    console.error('Error fetching students:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/admin/students - Create a new student\nexport async function POST(request: NextRequest) {\n  try {\n    // Temporarily bypass authentication for testing\n    // const session = await getServerSession(authOptions);\n    // if (!session?.user) {\n    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\n    // }\n\n    // if (!hasPermission(session.user.role, 'students:write')) {\n    //   return NextResponse.json({ error: 'Forbidden' }, { status: 403 });\n    // }\n\n    // Parse and validate request body\n    const body = await request.json();\n    const validatedData = createStudentSchema.parse(body);\n\n    // Check if email already exists\n    const existingUser = await prisma.user.findUnique({\n      where: { email: validatedData.email }\n    });\n\n    if (existingUser) {\n      return NextResponse.json(\n        { error: 'A user with this email already exists' },\n        { status: 400 }\n      );\n    }\n\n    // Parse class and section from classId (format: \"classId-sectionId\")\n    const [classId, sectionId] = validatedData.classId.split('-');\n    \n    // Check if class exists\n    const classExists = await prisma.class.findUnique({\n      where: { id: parseInt(classId) }\n    });\n\n    if (!classExists) {\n      return NextResponse.json(\n        { error: 'Class not found' },\n        { status: 400 }\n      );\n    }\n\n    // Check if section exists\n    if (sectionId) {\n      const sectionExists = await prisma.section.findUnique({\n        where: { id: parseInt(sectionId) }\n      });\n\n      if (!sectionExists) {\n        return NextResponse.json(\n          { error: 'Section not found' },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Generate a secure default password\n    const defaultPassword = 'Student@12345';\n    const hashedPassword = await bcrypt.hash(defaultPassword, 12);\n    \n    // Create user account for the student\n    const user = await prisma.user.create({\n      data: {\n        email: validatedData.email,\n        hashedPassword: hashedPassword,\n        role: 'STUDENT',\n        firstName: validatedData.firstName,\n        lastName: validatedData.lastName,\n        phone: validatedData.phoneNumber,\n      }\n    });\n\n    // Create student record\n    const student = await prisma.student.create({\n      data: {\n        userId: user.id,\n        admissionNo: `STU${Date.now()}`, // Generate unique admission number\n        dateOfBirth: new Date(validatedData.dateOfBirth),\n        gender: validatedData.gender,\n        address: validatedData.address,\n        guardianName: validatedData.parentName || 'Guardian',\n        guardianPhone: validatedData.parentPhone || '',\n        currentClassId: parseInt(classId),\n        currentSectionId: sectionId ? parseInt(sectionId) : null,\n        rollNumber: validatedData.rollNumber,\n      },\n      include: {\n        user: true,\n        currentClass: true,\n        currentSection: true,\n      }\n    });\n\n    return NextResponse.json(\n      { \n        message: 'Student created successfully',\n        student,\n      },\n      { status: 201 }\n    );\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.format() },\n        { status: 400 }\n      );\n    }\n\n    console.error('Error creating student:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAGA;AACA;AAEA;;;;;AAEA,2CAA2C;AAC3C,MAAM,sBAAsB,sQAAC,CAAC,MAAM,CAAC;IACnC,WAAW,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,sQAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,aAAa,sQAAC,CAAC,MAAM,GAAG,KAAK,CAAC,uBAAuB;IACrD,QAAQ,sQAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAU;KAAQ;IAC1C,aAAa,sQAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,SAAS,sQAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,SAAS,sQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,YAAY,sQAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,aAAa,sQAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,YAAY,sQAAC,CAAC,MAAM,GAAG,QAAQ;AACjC;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,gDAAgD;QAChD,uDAAuD;QACvD,wBAAwB;QACxB,0EAA0E;QAC1E,IAAI;QAEJ,4DAA4D;QAC5D,uEAAuE;QACvE,IAAI;QAEJ,yBAAyB;QACzB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,UAAU,aAAa,GAAG,CAAC,cAAc;QAC/C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAE7C,uCAAuC;QACvC,MAAM,WAAW,MAAM,8JAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,SAAS;gBACP,MAAM;gBACN,cAAc;gBACd,gBAAgB;YAClB;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;QAEA,gBAAgB;QAChB,IAAI,mBAAmB;QAEvB,IAAI,QAAQ;YACV,mBAAmB,SAAS,MAAM,CAAC,CAAA,UACjC,QAAQ,IAAI,EAAE,WAAW,cAAc,SAAS,OAAO,WAAW,OAClE,QAAQ,IAAI,EAAE,UAAU,cAAc,SAAS,OAAO,WAAW,OACjE,QAAQ,IAAI,EAAE,OAAO,cAAc,SAAS,OAAO,WAAW;QAElE;QAEA,IAAI,SAAS;YACX,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,cAAc,KAAK,SAAS;QAExC;QAEA,IAAI,QAAQ;YACV,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,MAAM,KAAK;QAEvB;QAEA,mBAAmB;QACnB,MAAM,aAAa,iBAAiB,MAAM;QAC1C,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;QAC1C,MAAM,cAAc,OAAO;QAC3B,MAAM,cAAc,OAAO;QAE3B,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAC9B,MAAM,oBAAoB,iBAAiB,KAAK,CAAC,YAAY;QAE7D,OAAO,iSAAY,CAAC,IAAI,CAAC;YACvB,UAAU;YACV,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,iSAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,gDAAgD;QAChD,uDAAuD;QACvD,wBAAwB;QACxB,0EAA0E;QAC1E,IAAI;QAEJ,6DAA6D;QAC7D,uEAAuE;QACvE,IAAI;QAEJ,kCAAkC;QAClC,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,oBAAoB,KAAK,CAAC;QAEhD,gCAAgC;QAChC,MAAM,eAAe,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,OAAO,cAAc,KAAK;YAAC;QACtC;QAEA,IAAI,cAAc;YAChB,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,qEAAqE;QACrE,MAAM,CAAC,SAAS,UAAU,GAAG,cAAc,OAAO,CAAC,KAAK,CAAC;QAEzD,wBAAwB;QACxB,MAAM,cAAc,MAAM,8JAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,IAAI,SAAS;YAAS;QACjC;QAEA,IAAI,CAAC,aAAa;YAChB,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,0BAA0B;QAC1B,IAAI,WAAW;YACb,MAAM,gBAAgB,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBACpD,OAAO;oBAAE,IAAI,SAAS;gBAAW;YACnC;YAEA,IAAI,CAAC,eAAe;gBAClB,OAAO,iSAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoB,GAC7B;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,qCAAqC;QACrC,MAAM,kBAAkB;QACxB,MAAM,iBAAiB,MAAM,qOAAM,CAAC,IAAI,CAAC,iBAAiB;QAE1D,sCAAsC;QACtC,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,OAAO,cAAc,KAAK;gBAC1B,gBAAgB;gBAChB,MAAM;gBACN,WAAW,cAAc,SAAS;gBAClC,UAAU,cAAc,QAAQ;gBAChC,OAAO,cAAc,WAAW;YAClC;QACF;QAEA,wBAAwB;QACxB,MAAM,UAAU,MAAM,8JAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ,QAAQ,KAAK,EAAE;gBACf,aAAa,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI;gBAC/B,aAAa,IAAI,KAAK,cAAc,WAAW;gBAC/C,QAAQ,cAAc,MAAM;gBAC5B,SAAS,cAAc,OAAO;gBAC9B,cAAc,cAAc,UAAU,IAAI;gBAC1C,eAAe,cAAc,WAAW,IAAI;gBAC5C,gBAAgB,SAAS;gBACzB,kBAAkB,YAAY,SAAS,aAAa;gBACpD,YAAY,cAAc,UAAU;YACtC;YACA,SAAS;gBACP,MAAM;gBACN,cAAc;gBACd,gBAAgB;YAClB;QACF;QAEA,OAAO,iSAAY,CAAC,IAAI,CACtB;YACE,SAAS;YACT;QACF,GACA;YAAE,QAAQ;QAAI;IAElB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,sQAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAG,GACrD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,iSAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}