// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/" | "/admin" | "/admin/attendance" | "/admin/classes" | "/admin/classes/new" | "/admin/exams" | "/admin/marks" | "/admin/reports" | "/admin/settings" | "/admin/students" | "/admin/students/[id]" | "/admin/students/[id]/edit" | "/admin/students/bulk" | "/admin/students/new" | "/admin/subjects" | "/admin/teachers" | "/admin/teachers/[id]" | "/admin/teachers/[id]/edit" | "/admin/teachers/new" | "/login" | "/student" | "/student/attendance" | "/student/marks" | "/student/reports" | "/teacher" | "/teacher/attendance" | "/teacher/attendance/mark" | "/test-login" | "/unauthorized"
type AppRouteHandlerRoutes = "/api/admin/attendance" | "/api/admin/classes" | "/api/admin/classes/[id]" | "/api/admin/exams" | "/api/admin/marks" | "/api/admin/reports" | "/api/admin/sections" | "/api/admin/settings" | "/api/admin/students" | "/api/admin/students/[id]" | "/api/admin/students/bulk" | "/api/admin/subjects" | "/api/admin/teachers" | "/api/admin/teachers/[id]" | "/api/auth/[...nextauth]" | "/api/student/attendance" | "/api/student/marks" | "/api/student/reports" | "/api/teacher/attendance"
type PageRoutes = never
type LayoutRoutes = "/"
type RedirectRoutes = never
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes | AppRouteHandlerRoutes


interface ParamMap {
  "/": {}
  "/admin": {}
  "/admin/attendance": {}
  "/admin/classes": {}
  "/admin/classes/new": {}
  "/admin/exams": {}
  "/admin/marks": {}
  "/admin/reports": {}
  "/admin/settings": {}
  "/admin/students": {}
  "/admin/students/[id]": { "id": string; }
  "/admin/students/[id]/edit": { "id": string; }
  "/admin/students/bulk": {}
  "/admin/students/new": {}
  "/admin/subjects": {}
  "/admin/teachers": {}
  "/admin/teachers/[id]": { "id": string; }
  "/admin/teachers/[id]/edit": { "id": string; }
  "/admin/teachers/new": {}
  "/api/admin/attendance": {}
  "/api/admin/classes": {}
  "/api/admin/classes/[id]": { "id": string; }
  "/api/admin/exams": {}
  "/api/admin/marks": {}
  "/api/admin/reports": {}
  "/api/admin/sections": {}
  "/api/admin/settings": {}
  "/api/admin/students": {}
  "/api/admin/students/[id]": { "id": string; }
  "/api/admin/students/bulk": {}
  "/api/admin/subjects": {}
  "/api/admin/teachers": {}
  "/api/admin/teachers/[id]": { "id": string; }
  "/api/auth/[...nextauth]": { "nextauth": string[]; }
  "/api/student/attendance": {}
  "/api/student/marks": {}
  "/api/student/reports": {}
  "/api/teacher/attendance": {}
  "/login": {}
  "/student": {}
  "/student/attendance": {}
  "/student/marks": {}
  "/student/reports": {}
  "/teacher": {}
  "/teacher/attendance": {}
  "/teacher/attendance/mark": {}
  "/test-login": {}
  "/unauthorized": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap, AppRouteHandlerRoutes }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }

  /**
   * Context for Next.js App Router route handlers
   * @example
   * ```tsx
   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {
   *   const { id } = await context.params
   *   return Response.json({ id })
   * }
   * ```
   */
  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {
    params: Promise<ParamMap[AppRouteHandlerRoute]>
  }
}
