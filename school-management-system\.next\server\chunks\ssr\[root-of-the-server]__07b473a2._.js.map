{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sQAAO,EAAC,IAAA,gOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,oRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oWAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,+UAAI,GAAG;IAC9B,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,oWAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,oWAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAA<PERSON>;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,oWAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,oWAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QAAI,KAAK;QAAK,WAAW,IAAA,2JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-gray-900 dark:[&>svg]:text-gray-100\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 border-gray-200 dark:border-gray-800\",\r\n        destructive:\r\n          \"border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,IAAA,oRAAG,EACvB,sLACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,oWAAgB,CAG5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,+XAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,IAAA,2JAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,oWAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,oWAAgB,CAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC;QACC,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,oWAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,+XAAC;QACC,MAAM;QACN,WAAW,IAAA,2JAAE,EACX,yaACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        secondary:\r\n          \"border-transparent bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        destructive:\r\n          \"border-transparent bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline: \"text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-700\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,IAAA,oRAAG,EACvB,8KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,+XAAC;QAAI,WAAW,IAAA,2JAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/teachers/teacher-table.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\n\ninterface Teacher {\n  id: number;\n  firstName: string;\n  lastName: string;\n  email: string;\n  phone?: string;\n  gender?: string;\n  qualification?: string;\n  experience?: number;\n  salary?: number;\n  isActive: boolean;\n  user: {\n    id: number;\n    email: string;\n    role: string;\n  };\n  classes: Array<{\n    id: number;\n    name: string;\n    section: {\n      id: number;\n      name: string;\n    };\n  }>;\n  subjects: Array<{\n    id: number;\n    name: string;\n  }>;\n}\n\ninterface TeacherTableProps {\n  teachers: Teacher[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n  onPageChange: (page: number) => void;\n  onSearch: (search: string) => void;\n  onFilter: (filter: string) => void;\n  loading?: boolean;\n}\n\nexport default function TeacherTable({\n  teachers,\n  pagination,\n  onPageChange,\n  onSearch,\n  onFilter,\n  loading = false,\n}: TeacherTableProps) {\n  const router = useRouter();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterActive, setFilterActive] = useState<string>('all');\n  const [deleteLoading, setDeleteLoading] = useState<number | null>(null);\n\n  const handleSearch = (value: string) => {\n    setSearchTerm(value);\n    onSearch(value);\n  };\n\n  const handleFilter = (value: string) => {\n    setFilterActive(value);\n    onFilter(value);\n  };\n\n  const handleDelete = async (teacherId: number) => {\n    if (!confirm('Are you sure you want to delete this teacher? This action cannot be undone.')) {\n      return;\n    }\n\n    setDeleteLoading(teacherId);\n    try {\n      const response = await fetch(`/api/admin/teachers/${teacherId}`, {\n        method: 'DELETE',\n      });\n\n      if (!response.ok) {\n        const data = await response.json();\n        throw new Error(data.error || 'Failed to delete teacher');\n      }\n\n      // Refresh the page to show updated data\n      window.location.reload();\n    } catch (error: any) {\n      alert(error.message);\n    } finally {\n      setDeleteLoading(null);\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  const getGenderLabel = (gender: string) => {\n    switch (gender) {\n      case 'MALE': return 'Male';\n      case 'FEMALE': return 'Female';\n      case 'OTHER': return 'Other';\n      default: return 'Not specified';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <div className=\"flex justify-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"></div>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n          <CardTitle>Teachers ({pagination.total})</CardTitle>\n          <div className=\"flex flex-col sm:flex-row gap-2 w-full sm:w-auto\">\n            <Input\n              placeholder=\"Search teachers...\"\n              value={searchTerm}\n              onChange={(e) => handleSearch(e.target.value)}\n              className=\"w-full sm:w-64\"\n            />\n            <select\n              value={filterActive}\n              onChange={(e) => handleFilter(e.target.value)}\n              className=\"w-full sm:w-auto p-2 border border-gray-300 rounded-md\"\n            >\n              <option value=\"all\">All Teachers</option>\n              <option value=\"true\">Active Only</option>\n              <option value=\"false\">Inactive Only</option>\n            </select>\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {teachers.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <p className=\"text-gray-500\">No teachers found.</p>\n          </div>\n        ) : (\n          <>\n            {/* Desktop Table */}\n            <div className=\"hidden lg:block overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr className=\"border-b\">\n                    <th className=\"text-left p-2\">Name</th>\n                    <th className=\"text-left p-2\">Email</th>\n                    <th className=\"text-left p-2\">Phone</th>\n                    <th className=\"text-left p-2\">Gender</th>\n                    <th className=\"text-left p-2\">Qualification</th>\n                    <th className=\"text-left p-2\">Experience</th>\n                    <th className=\"text-left p-2\">Classes</th>\n                    <th className=\"text-left p-2\">Status</th>\n                    <th className=\"text-left p-2\">Actions</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {teachers.map((teacher) => (\n                    <tr key={teacher.id} className=\"border-b hover:bg-gray-50\">\n                      <td className=\"p-2\">\n                        <div>\n                          <div className=\"font-medium\">\n                            {teacher.firstName} {teacher.lastName}\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"p-2\">{teacher.email}</td>\n                      <td className=\"p-2\">{teacher.phone || '-'}</td>\n                      <td className=\"p-2\">\n                        {teacher.gender ? getGenderLabel(teacher.gender) : '-'}\n                      </td>\n                      <td className=\"p-2\">{teacher.qualification || '-'}</td>\n                      <td className=\"p-2\">\n                        {teacher.experience ? `${teacher.experience} years` : '-'}\n                      </td>\n                      <td className=\"p-2\">\n                        <div className=\"flex flex-wrap gap-1\">\n                          {teacher.classes.length > 0 ? (\n                            teacher.classes.map((cls) => (\n                              <Badge key={cls.id} variant=\"secondary\" className=\"text-xs\">\n                                {cls.name} {cls.section.name}\n                              </Badge>\n                            ))\n                          ) : (\n                            <span className=\"text-gray-500 text-sm\">No classes</span>\n                          )}\n                        </div>\n                      </td>\n                      <td className=\"p-2\">\n                        <Badge variant={teacher.isActive ? 'default' : 'secondary'}>\n                          {teacher.isActive ? 'Active' : 'Inactive'}\n                        </Badge>\n                      </td>\n                      <td className=\"p-2\">\n                        <div className=\"flex gap-2\">\n                          <Button\n                            size=\"sm\"\n                            variant=\"outline\"\n                            onClick={() => router.push(`/admin/teachers/${teacher.id}`)}\n                          >\n                            View\n                          </Button>\n                          <Button\n                            size=\"sm\"\n                            variant=\"outline\"\n                            onClick={() => router.push(`/admin/teachers/${teacher.id}/edit`)}\n                          >\n                            Edit\n                          </Button>\n                          <Button\n                            size=\"sm\"\n                            variant=\"destructive\"\n                            onClick={() => handleDelete(teacher.id)}\n                            disabled={deleteLoading === teacher.id}\n                          >\n                            {deleteLoading === teacher.id ? 'Deleting...' : 'Delete'}\n                          </Button>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Mobile Cards */}\n            <div className=\"lg:hidden space-y-4\">\n              {teachers.map((teacher) => (\n                <Card key={teacher.id} className=\"p-4\">\n                  <div className=\"flex flex-col space-y-3\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1 min-w-0\">\n                        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 truncate\">\n                          {teacher.firstName} {teacher.lastName}\n                        </h3>\n                        <p className=\"text-sm text-gray-500 dark:text-gray-400 truncate\">\n                          {teacher.email}\n                        </p>\n                      </div>\n                      <div className=\"flex items-center ml-4\">\n                        <Badge variant={teacher.isActive ? 'default' : 'secondary'}>\n                          {teacher.isActive ? 'Active' : 'Inactive'}\n                        </Badge>\n                      </div>\n                    </div>\n                    \n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"font-medium text-gray-700 dark:text-gray-300\">Phone:</span>\n                        <p className=\"text-gray-600 dark:text-gray-400\">{teacher.phone || '-'}</p>\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-gray-700 dark:text-gray-300\">Gender:</span>\n                        <p className=\"text-gray-600 dark:text-gray-400\">\n                          {teacher.gender ? getGenderLabel(teacher.gender) : '-'}\n                        </p>\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-gray-700 dark:text-gray-300\">Qualification:</span>\n                        <p className=\"text-gray-600 dark:text-gray-400\">{teacher.qualification || '-'}</p>\n                      </div>\n                      <div>\n                        <span className=\"font-medium text-gray-700 dark:text-gray-300\">Experience:</span>\n                        <p className=\"text-gray-600 dark:text-gray-400\">\n                          {teacher.experience ? `${teacher.experience} years` : '-'}\n                        </p>\n                      </div>\n                    </div>\n\n                    <div>\n                      <span className=\"font-medium text-gray-700 dark:text-gray-300 text-sm\">Classes:</span>\n                      <div className=\"flex flex-wrap gap-1 mt-1\">\n                        {teacher.classes.length > 0 ? (\n                          teacher.classes.map((cls) => (\n                            <Badge key={cls.id} variant=\"secondary\" className=\"text-xs\">\n                              {cls.name} {cls.section.name}\n                            </Badge>\n                          ))\n                        ) : (\n                          <span className=\"text-gray-500 text-sm\">No classes assigned</span>\n                        )}\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        className=\"flex-1\"\n                        onClick={() => router.push(`/admin/teachers/${teacher.id}`)}\n                      >\n                        View\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        className=\"flex-1\"\n                        onClick={() => router.push(`/admin/teachers/${teacher.id}/edit`)}\n                      >\n                        Edit\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"destructive\"\n                        className=\"flex-1\"\n                        onClick={() => handleDelete(teacher.id)}\n                        disabled={deleteLoading === teacher.id}\n                      >\n                        {deleteLoading === teacher.id ? 'Del...' : 'Delete'}\n                      </Button>\n                    </div>\n                  </div>\n                </Card>\n              ))}\n            </div>\n          </>\n        )}\n\n        {/* Pagination */}\n        {pagination.totalPages > 1 && (\n          <div className=\"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0 mt-6\">\n            <div className=\"text-sm text-gray-600 text-center sm:text-left\">\n              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}\n              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}\n              {pagination.total} teachers\n            </div>\n            <div className=\"flex justify-center sm:justify-end gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => onPageChange(pagination.page - 1)}\n                disabled={pagination.page <= 1}\n              >\n                <span className=\"hidden sm:inline\">Previous</span>\n                <span className=\"sm:hidden\">Prev</span>\n              </Button>\n              <div className=\"flex items-center px-3 py-2 text-sm bg-gray-50 rounded-md\">\n                <span className=\"hidden sm:inline\">Page </span>\n                {pagination.page} <span className=\"hidden sm:inline\">of {pagination.totalPages}</span>\n                <span className=\"sm:hidden\">/{pagination.totalPages}</span>\n              </div>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => onPageChange(pagination.page + 1)}\n                disabled={pagination.page >= pagination.totalPages}\n              >\n                <span className=\"hidden sm:inline\">Next</span>\n                <span className=\"sm:hidden\">Next</span>\n              </Button>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAsDe,SAAS,aAAa,EACnC,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACG;IAClB,MAAM,SAAS,IAAA,gSAAS;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,kWAAQ,EAAC;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,kWAAQ,EAAS;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,kWAAQ,EAAgB;IAElE,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,gBAAgB;QAChB,SAAS;IACX;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,gFAAgF;YAC3F;QACF;QAEA,iBAAiB;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,WAAW,EAAE;gBAC/D,QAAQ;YACV;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,wCAAwC;YACxC,OAAO,QAAQ,CAAC,MAAM;QACxB,EAAE,OAAO,OAAY;YACnB,MAAM,MAAM,OAAO;QACrB,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB;IAChD;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,+XAAC,0KAAI;sBACH,cAAA,+XAAC,iLAAW;gBAAC,WAAU;0BACrB,cAAA,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,+XAAC,0KAAI;;0BACH,+XAAC,gLAAU;0BACT,cAAA,+XAAC;oBAAI,WAAU;;sCACb,+XAAC,+KAAS;;gCAAC;gCAAW,WAAW,KAAK;gCAAC;;;;;;;sCACvC,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,4KAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAU;;;;;;8CAEZ,+XAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAU;;sDAEV,+XAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,+XAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,+XAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAK9B,+XAAC,iLAAW;;oBACT,SAAS,MAAM,KAAK,kBACnB,+XAAC;wBAAI,WAAU;kCACb,cAAA,+XAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;6CAG/B;;0CAEE,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAM,WAAU;;sDACf,+XAAC;sDACC,cAAA,+XAAC;gDAAG,WAAU;;kEACZ,+XAAC;wDAAG,WAAU;kEAAgB;;;;;;kEAC9B,+XAAC;wDAAG,WAAU;kEAAgB;;;;;;kEAC9B,+XAAC;wDAAG,WAAU;kEAAgB;;;;;;kEAC9B,+XAAC;wDAAG,WAAU;kEAAgB;;;;;;kEAC9B,+XAAC;wDAAG,WAAU;kEAAgB;;;;;;kEAC9B,+XAAC;wDAAG,WAAU;kEAAgB;;;;;;kEAC9B,+XAAC;wDAAG,WAAU;kEAAgB;;;;;;kEAC9B,+XAAC;wDAAG,WAAU;kEAAgB;;;;;;kEAC9B,+XAAC;wDAAG,WAAU;kEAAgB;;;;;;;;;;;;;;;;;sDAGlC,+XAAC;sDACE,SAAS,GAAG,CAAC,CAAC,wBACb,+XAAC;oDAAoB,WAAU;;sEAC7B,+XAAC;4DAAG,WAAU;sEACZ,cAAA,+XAAC;0EACC,cAAA,+XAAC;oEAAI,WAAU;;wEACZ,QAAQ,SAAS;wEAAC;wEAAE,QAAQ,QAAQ;;;;;;;;;;;;;;;;;sEAI3C,+XAAC;4DAAG,WAAU;sEAAO,QAAQ,KAAK;;;;;;sEAClC,+XAAC;4DAAG,WAAU;sEAAO,QAAQ,KAAK,IAAI;;;;;;sEACtC,+XAAC;4DAAG,WAAU;sEACX,QAAQ,MAAM,GAAG,eAAe,QAAQ,MAAM,IAAI;;;;;;sEAErD,+XAAC;4DAAG,WAAU;sEAAO,QAAQ,aAAa,IAAI;;;;;;sEAC9C,+XAAC;4DAAG,WAAU;sEACX,QAAQ,UAAU,GAAG,GAAG,QAAQ,UAAU,CAAC,MAAM,CAAC,GAAG;;;;;;sEAExD,+XAAC;4DAAG,WAAU;sEACZ,cAAA,+XAAC;gEAAI,WAAU;0EACZ,QAAQ,OAAO,CAAC,MAAM,GAAG,IACxB,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,oBACnB,+XAAC,4KAAK;wEAAc,SAAQ;wEAAY,WAAU;;4EAC/C,IAAI,IAAI;4EAAC;4EAAE,IAAI,OAAO,CAAC,IAAI;;uEADlB,IAAI,EAAE;;;;8FAKpB,+XAAC;oEAAK,WAAU;8EAAwB;;;;;;;;;;;;;;;;sEAI9C,+XAAC;4DAAG,WAAU;sEACZ,cAAA,+XAAC,4KAAK;gEAAC,SAAS,QAAQ,QAAQ,GAAG,YAAY;0EAC5C,QAAQ,QAAQ,GAAG,WAAW;;;;;;;;;;;sEAGnC,+XAAC;4DAAG,WAAU;sEACZ,cAAA,+XAAC;gEAAI,WAAU;;kFACb,+XAAC,8KAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE;kFAC3D;;;;;;kFAGD,+XAAC,8KAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC;kFAChE;;;;;;kFAGD,+XAAC,8KAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,SAAS,IAAM,aAAa,QAAQ,EAAE;wEACtC,UAAU,kBAAkB,QAAQ,EAAE;kFAErC,kBAAkB,QAAQ,EAAE,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;mDAzD/C,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0CAoE3B,+XAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,+XAAC,0KAAI;wCAAkB,WAAU;kDAC/B,cAAA,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAI,WAAU;;8EACb,+XAAC;oEAAG,WAAU;;wEACX,QAAQ,SAAS;wEAAC;wEAAE,QAAQ,QAAQ;;;;;;;8EAEvC,+XAAC;oEAAE,WAAU;8EACV,QAAQ,KAAK;;;;;;;;;;;;sEAGlB,+XAAC;4DAAI,WAAU;sEACb,cAAA,+XAAC,4KAAK;gEAAC,SAAS,QAAQ,QAAQ,GAAG,YAAY;0EAC5C,QAAQ,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;8DAKrC,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;;8EACC,+XAAC;oEAAK,WAAU;8EAA+C;;;;;;8EAC/D,+XAAC;oEAAE,WAAU;8EAAoC,QAAQ,KAAK,IAAI;;;;;;;;;;;;sEAEpE,+XAAC;;8EACC,+XAAC;oEAAK,WAAU;8EAA+C;;;;;;8EAC/D,+XAAC;oEAAE,WAAU;8EACV,QAAQ,MAAM,GAAG,eAAe,QAAQ,MAAM,IAAI;;;;;;;;;;;;sEAGvD,+XAAC;;8EACC,+XAAC;oEAAK,WAAU;8EAA+C;;;;;;8EAC/D,+XAAC;oEAAE,WAAU;8EAAoC,QAAQ,aAAa,IAAI;;;;;;;;;;;;sEAE5E,+XAAC;;8EACC,+XAAC;oEAAK,WAAU;8EAA+C;;;;;;8EAC/D,+XAAC;oEAAE,WAAU;8EACV,QAAQ,UAAU,GAAG,GAAG,QAAQ,UAAU,CAAC,MAAM,CAAC,GAAG;;;;;;;;;;;;;;;;;;8DAK5D,+XAAC;;sEACC,+XAAC;4DAAK,WAAU;sEAAuD;;;;;;sEACvE,+XAAC;4DAAI,WAAU;sEACZ,QAAQ,OAAO,CAAC,MAAM,GAAG,IACxB,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,oBACnB,+XAAC,4KAAK;oEAAc,SAAQ;oEAAY,WAAU;;wEAC/C,IAAI,IAAI;wEAAC;wEAAE,IAAI,OAAO,CAAC,IAAI;;mEADlB,IAAI,EAAE;;;;0FAKpB,+XAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;8DAK9C,+XAAC;oDAAI,WAAU;;sEACb,+XAAC,8KAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE;sEAC3D;;;;;;sEAGD,+XAAC,8KAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC;sEAChE;;;;;;sEAGD,+XAAC,8KAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAU;4DACV,SAAS,IAAM,aAAa,QAAQ,EAAE;4DACtC,UAAU,kBAAkB,QAAQ,EAAE;sEAErC,kBAAkB,QAAQ,EAAE,GAAG,WAAW;;;;;;;;;;;;;;;;;;uCAhFxC,QAAQ,EAAE;;;;;;;;;;;;oBA2F5B,WAAW,UAAU,GAAG,mBACvB,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAI,WAAU;;oCAAiD;oCACpD,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAI;oCAAE;oCAAI;oCAC3D,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,WAAW,KAAK;oCAAE;oCAAI;oCACnE,WAAW,KAAK;oCAAC;;;;;;;0CAEpB,+XAAC;gCAAI,WAAU;;kDACb,+XAAC,8KAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa,WAAW,IAAI,GAAG;wCAC9C,UAAU,WAAW,IAAI,IAAI;;0DAE7B,+XAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,+XAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;kDAE9B,+XAAC;wCAAI,WAAU;;0DACb,+XAAC;gDAAK,WAAU;0DAAmB;;;;;;4CAClC,WAAW,IAAI;4CAAC;0DAAC,+XAAC;gDAAK,WAAU;;oDAAmB;oDAAI,WAAW,UAAU;;;;;;;0DAC9E,+XAAC;gDAAK,WAAU;;oDAAY;oDAAE,WAAW,UAAU;;;;;;;;;;;;;kDAErD,+XAAC,8KAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa,WAAW,IAAI,GAAG;wCAC9C,UAAU,WAAW,IAAI,IAAI,WAAW,UAAU;;0DAElD,+XAAC;gDAAK,WAAU;0DAAmB;;;;;;0DACnC,+XAAC;gDAAK,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C", "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,eAAe,6TAA0B;AAE/C,MAAM,sBAAsB,gUAA6B;AAEzD,MAAM,oBAAoB,8TAA2B;AAErD,MAAM,qBAAqB,+TAA4B;AAEvD,MAAM,kBAAkB,4TAAyB;AAEjD,MAAM,yBAAyB,mUAAgC;AAE/D,MAAM,uCAAyB,oWAAgB,CAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,+XAAC,mUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,+XAAC,4VAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,mUAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,oWAAgB,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,mUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAAG,mUAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,oWAAgB,CAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,+XAAC,+TAA4B;kBAC3B,cAAA,+XAAC,gUAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,2JAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,gUAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,oWAAgB,CAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,+XAAC,6TAA0B;QACzB,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,6TAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,oWAAgB,CAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,+XAAC,qUAAkC;QACjC,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,+XAAC;gBAAK,WAAU;0BACd,cAAA,+XAAC,sUAAmC;8BAClC,cAAA,+XAAC,mUAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAAG,qUAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,oWAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,+XAAC,kUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,+XAAC;gBAAK,WAAU;0BACd,cAAA,+XAAC,sUAAmC;8BAClC,cAAA,+XAAC,sUAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,kUAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,oWAAgB,CAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,+XAAC,8TAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,2JAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,8TAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,oWAAgB,CAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,kUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,2JAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,kUAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,+XAAC;QACC,WAAW,IAAA,2JAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, Sun, Monitor } from 'lucide-react'\nimport { Button } from './button'\nimport { useTheme } from '@/components/providers/theme-provider'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from './dropdown-menu'\n\nexport function ThemeToggle() {\n  const { theme, actualTheme, mounted, setTheme } = useTheme()\n\n  const toggleTheme = () => {\n    if (!mounted) return\n    const next = actualTheme === 'light' ? 'dark' : 'light'\n    setTheme(next)\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-9 w-9\"\n          onClick={toggleTheme}\n          title={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme('light')}>\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('dark')}>\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('system')}>\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAYO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAA,kMAAQ;IAE1D,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,OAAO,gBAAgB,UAAU,SAAS;QAChD,SAAS;IACX;IAEA,qBACE,+XAAC,8LAAY;;0BACX,+XAAC,qMAAmB;gBAAC,OAAO;0BAC1B,cAAA,+XAAC,8KAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,OAAO,CAAC,UAAU,EAAE,gBAAgB,UAAU,SAAS,QAAQ,KAAK,CAAC;;sCAErE,+XAAC,6TAAG;4BAAC,WAAU;;;;;;sCACf,+XAAC,gUAAI;4BAAC,WAAU;;;;;;sCAChB,+XAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,+XAAC,qMAAmB;gBAAC,OAAM;;kCACzB,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,6TAAG;gCAAC,WAAU;;;;;;0CACf,+XAAC;0CAAK;;;;;;;;;;;;kCAER,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,gUAAI;gCAAC,WAAU;;;;;;0CAChB,+XAAC;0CAAK;;;;;;;;;;;;kCAER,+XAAC,kMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,+XAAC,yUAAO;gCAAC,WAAU;;;;;;0CACnB,+XAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 1580, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession, signOut } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\nimport {\n  Menu,\n  X,\n  User,\n  LogOut,\n  Settings,\n  Bell,\n  Search,\n  School,\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Edit,\n  ClipboardList,\n  Award\n} from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  navigation: {\n    name: string\n    href: string\n    icon: string\n  }[]\n}\n\n// Icon mapping object\nconst iconMap: Record<string, React.ComponentType<{ className?: string }>> = {\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Settings,\n  Bell,\n  User,\n  Edit,\n  ClipboardList,\n  Award\n}\n\nexport default function DashboardLayout({ children, title, navigation }: DashboardLayoutProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' })\n  }\n\n  const getIcon = (iconName: string) => {\n    const IconComponent = iconMap[iconName]\n    return IconComponent || Home // fallback to Home icon if not found\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-950\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <School className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-lg font-semibold\">SMS</span>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={item.name}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => {\n                    router.push(item.href)\n                    setSidebarOpen(false)\n                  }}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\">\n          <div className=\"flex h-16 items-center px-4\">\n            <School className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-lg font-semibold hidden xl:inline\">School Management System</span>\n            <span className=\"ml-2 text-lg font-semibold xl:hidden\">SMS</span>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={item.name}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => router.push(item.href)}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1\">\n              <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n            <ThemeToggle />\n\n            <Button variant=\"ghost\" size=\"sm\">\n              <Bell className=\"h-5 w-5\" />\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"flex items-center gap-x-3\">\n                <div className=\"text-sm hidden sm:block\">\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\n                    {session?.user?.firstName} {session?.user?.lastName}\n                  </p>\n                  <p className=\"text-gray-500 dark:text-gray-400 capitalize\">\n                    {session?.user?.role?.toLowerCase()}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleSignOut}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\n            </div>\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AA0CA,sBAAsB;AACtB,MAAM,UAAuE;IAC3E,MAAA,gUAAI;IACJ,QAAA,sUAAM;IACN,UAAA,4UAAQ;IACR,OAAA,mUAAK;IACL,UAAA,gVAAQ;IACR,eAAA,+VAAa;IACb,UAAA,gVAAQ;IACR,WAAA,qVAAS;IACT,UAAA,4UAAQ;IACR,MAAA,iUAAI;IACJ,UAAA,4UAAQ;IACR,MAAA,gUAAI;IACJ,MAAA,gUAAI;IACJ,MAAA,yUAAI;IACJ,eAAA,+VAAa;IACb,OAAA,mUAAK;AACP;AAEe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAwB;IAC3F,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,0SAAU;IACpC,MAAM,SAAS,IAAA,gSAAS;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,kWAAQ,EAAC;IAE/C,MAAM,gBAAgB;QACpB,MAAM,IAAA,uSAAO,EAAC;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,gBAAgB,OAAO,CAAC,SAAS;QACvC,OAAO,iBAAiB,iUAAI,CAAC,qCAAqC;;IACpE;IAEA,qBACE,+XAAC;QAAI,WAAU;;0BAEb,+XAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,UAAU,UAAU;;kCAChF,+XAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAI,WAAU;;0DACb,+XAAC,sUAAM;gDAAC,WAAU;;;;;;0DAClB,+XAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAE/C,+XAAC,8KAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;kDAE9B,cAAA,+XAAC,uTAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,+XAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;oCACvC,qBACE,+XAAC,8KAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,eAAe;wCACjB;;0DAEA,+XAAC;gDAAc,WAAU;;;;;;4CACxB,KAAK,IAAI;;uCATL,KAAK,IAAI;;;;;gCAYpB;;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAI,WAAU;0BACb,cAAA,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,sUAAM;oCAAC,WAAU;;;;;;8CAClB,+XAAC;oCAAK,WAAU;8CAA8C;;;;;;8CAC9D,+XAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,+XAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;gCACvC,qBACE,+XAAC,8KAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sDAEpC,+XAAC;4CAAc,WAAU;;;;;;wCACxB,KAAK,IAAI;;mCANL,KAAK,IAAI;;;;;4BASpB;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAI,WAAU;;kCAEb,+XAAC;wBAAI,WAAU;;0CACb,+XAAC,8KAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,+XAAC,gUAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;sDACb,cAAA,+XAAC,sUAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,+XAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,+XAAC;gCAAI,WAAU;;kDACb,+XAAC,4LAAW;;;;;kDAEZ,+XAAC,8KAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,+XAAC,gUAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAE,WAAU;;gEACV,SAAS,MAAM;gEAAU;gEAAE,SAAS,MAAM;;;;;;;sEAE7C,+XAAC;4DAAE,WAAU;sEACV,SAAS,MAAM,MAAM;;;;;;;;;;;;8DAG1B,+XAAC;oDAAI,WAAU;8DACb,cAAA,+XAAC,8KAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,+XAAC,0UAAM;gEAAC,WAAU;;;;;;0EAClB,+XAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,+XAAC;wBAAK,WAAU;kCACd,cAAA,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC;wCAAG,WAAU;kDAAuD;;;;;;;;;;;gCAEtE;;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 2064, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'FileText' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];\n\n/**\n * Get the default dashboard URL for a user role\n */\nexport function getRoleDashboardUrl(role: string): string {\n  switch (role) {\n    case 'ADMIN':\n      return '/admin'\n    case 'TEACHER':\n      return '/teacher'\n    case 'STUDENT':\n      return '/student'\n    default:\n      return '/'\n  }\n}\n\n/**\n * Get navigation items for a user role\n */\nexport function getRoleNavigation(role: string) {\n  switch (role) {\n    case 'ADMIN':\n      return adminNavigation\n    case 'TEACHER':\n      return teacherNavigation\n    case 'STUDENT':\n      return studentNavigation\n    default:\n      return []\n  }\n}"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAKM,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 2226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/admin/teachers/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport TeacherTable from '@/components/teachers/teacher-table';\nimport DashboardLayout from '@/components/layout/dashboard-layout';\nimport { adminNavigation } from '@/lib/navigation';\nimport { \n  Users, \n  GraduationCap, \n  BookOpen, \n  FileText, \n  Calendar,\n  BarChart3,\n  Settings,\n  UserPlus,\n  ClipboardList,\n  Award\n} from 'lucide-react';\n\ninterface Teacher {\n  id: number;\n  firstName: string;\n  lastName: string;\n  email: string;\n  phone?: string;\n  gender?: string;\n  qualification?: string;\n  experience?: number;\n  salary?: number;\n  isActive: boolean;\n  user: {\n    id: number;\n    email: string;\n    role: string;\n  };\n  classes: Array<{\n    id: number;\n    name: string;\n    section: {\n      id: number;\n      name: string;\n    };\n  }>;\n  subjects: Array<{\n    id: number;\n    name: string;\n  }>;\n}\n\nexport default function TeachersPage() {\n  const router = useRouter();\n  const [teachers, setTeachers] = useState<Teacher[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [pagination, setPagination] = useState({\n    page: 1,\n    limit: 10,\n    total: 0,\n    totalPages: 0,\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterActive, setFilterActive] = useState('all');\n\n  const fetchTeachers = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString(),\n        ...(searchTerm && { search: searchTerm }),\n        ...(filterActive !== 'all' && { isActive: filterActive }),\n      });\n\n      const response = await fetch(`/api/admin/teachers?${params}`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch teachers');\n      }\n\n      const data = await response.json();\n      setTeachers(data.teachers);\n      setPagination(data.pagination);\n    } catch (err: any) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchTeachers();\n  }, [pagination.page, searchTerm, filterActive]);\n\n  const handlePageChange = (page: number) => {\n    setPagination(prev => ({ ...prev, page }));\n  };\n\n  const handleSearch = (search: string) => {\n    setSearchTerm(search);\n    setPagination(prev => ({ ...prev, page: 1 }));\n  };\n\n  const handleFilter = (filter: string) => {\n    setFilterActive(filter);\n    setPagination(prev => ({ ...prev, page: 1 }));\n  };\n\n  return (\n    <DashboardLayout title=\"Teacher Management\" navigation={adminNavigation}>\n      <div className=\"space-y-6\">\n        <div className=\"flex justify-between items-center\">\n          <h1 className=\"text-3xl font-bold\">Teacher Management</h1>\n          <Button onClick={() => router.push('/admin/teachers/new')}>\n            Add New Teacher\n          </Button>\n        </div>\n\n        {error && (\n          <Alert variant=\"destructive\">\n            <AlertDescription>{error}</AlertDescription>\n          </Alert>\n        )}\n\n        <TeacherTable\n          teachers={teachers}\n          pagination={pagination}\n          onPageChange={handlePageChange}\n          onSearch={handleSearch}\n          onFilter={handleFilter}\n          loading={loading}\n        />\n\n        {/* Quick Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Teachers</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{pagination.total}</div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Active Teachers</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {teachers.filter(t => t.isActive).length}\n              </div>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Average Experience</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {teachers.length > 0 \n                  ? Math.round(teachers.reduce((sum, t) => sum + (t.experience || 0), 0) / teachers.length)\n                  : 0\n                } years\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAqDe,SAAS;IACtB,MAAM,SAAS,IAAA,gSAAS;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,kWAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,kWAAQ,EAAC;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,kWAAQ,EAAC;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,kWAAQ,EAAC;QAC3C,MAAM;QACN,OAAO;QACP,OAAO;QACP,YAAY;IACd;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,kWAAQ,EAAC;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,kWAAQ,EAAC;IAEjD,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,WAAW,IAAI,CAAC,QAAQ;gBAC9B,OAAO,WAAW,KAAK,CAAC,QAAQ;gBAChC,GAAI,cAAc;oBAAE,QAAQ;gBAAW,CAAC;gBACxC,GAAI,iBAAiB,SAAS;oBAAE,UAAU;gBAAa,CAAC;YAC1D;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,QAAQ;YAC5D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,YAAY,KAAK,QAAQ;YACzB,cAAc,KAAK,UAAU;QAC/B,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAA,mWAAS,EAAC;QACR;IACF,GAAG;QAAC,WAAW,IAAI;QAAE;QAAY;KAAa;IAE9C,MAAM,mBAAmB,CAAC;QACxB,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAK,CAAC;IAC1C;IAEA,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAE,CAAC;IAC7C;IAEA,MAAM,eAAe,CAAC;QACpB,gBAAgB;QAChB,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,MAAM;YAAE,CAAC;IAC7C;IAEA,qBACE,+XAAC,gMAAe;QAAC,OAAM;QAAqB,YAAY,6KAAe;kBACrE,cAAA,+XAAC;YAAI,WAAU;;8BACb,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,+XAAC,8KAAM;4BAAC,SAAS,IAAM,OAAO,IAAI,CAAC;sCAAwB;;;;;;;;;;;;gBAK5D,uBACC,+XAAC,4KAAK;oBAAC,SAAQ;8BACb,cAAA,+XAAC,uLAAgB;kCAAE;;;;;;;;;;;8BAIvB,+XAAC,+LAAY;oBACX,UAAU;oBACV,YAAY;oBACZ,cAAc;oBACd,UAAU;oBACV,UAAU;oBACV,SAAS;;;;;;8BAIX,+XAAC;oBAAI,WAAU;;sCACb,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;8CACpB,cAAA,+XAAC,+KAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;kDAAsB,WAAW,KAAK;;;;;;;;;;;;;;;;;sCAGzD,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;8CACpB,cAAA,+XAAC,+KAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;sCAI9C,+XAAC,0KAAI;;8CACH,+XAAC,gLAAU;oCAAC,WAAU;8CACpB,cAAA,+XAAC,+KAAS;wCAAC,WAAU;kDAAsB;;;;;;;;;;;8CAE7C,+XAAC,iLAAW;8CACV,cAAA,+XAAC;wCAAI,WAAU;;4CACZ,SAAS,MAAM,GAAG,IACf,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,UAAU,IAAI,CAAC,GAAG,KAAK,SAAS,MAAM,IACtF;4CACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB", "debugId": null}}]}