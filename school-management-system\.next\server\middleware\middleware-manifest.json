{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/school-management-system_8c1ea602._.js", "server/edge/chunks/[root-of-the-server]__433d4839._.js", "server/edge/chunks/turbopack-school-management-system_edge-wrapper_db311ba0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/teacher/:path*{(\\\\.json)}?", "originalSource": "/teacher/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/student/:path*{(\\\\.json)}?", "originalSource": "/student/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/admin/:path*{(\\\\.json)}?", "originalSource": "/api/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/teacher/:path*{(\\\\.json)}?", "originalSource": "/api/teacher/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/student/:path*{(\\\\.json)}?", "originalSource": "/api/student/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "O72Sp333G9CD2xefaiYCeef/o971MfYvqNDNBykp5go=", "__NEXT_PREVIEW_MODE_ID": "9181dc30862fd849023e51c815cf0696", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "911749a4e458d59180823a90957afbb9e73476ab02a917691df4741bc3092641", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "22cbaac110cc54a133586def25fe6333adf39bdf2d843ad7cd9ccffda762c593"}}}, "instrumentation": null, "functions": {}}