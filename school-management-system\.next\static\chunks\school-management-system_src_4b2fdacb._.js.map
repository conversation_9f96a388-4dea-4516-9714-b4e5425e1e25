{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yQAAO,EAAC,IAAA,mOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,4TAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,kVAAI,GAAG;IAC9B,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,4TAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,8UAAC;QACC,MAAM;QACN,WAAW,IAAA,8JAAE,EACX,yaACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,4TAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,4TAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,4TAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,4TAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QAAI,KAAK;QAAK,WAAW,IAAA,8JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,4TAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/test-login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { signIn, useSession } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\n\nexport default function TestLoginPage() {\n  const { data: session, status } = useSession()\n  const [credentials, setCredentials] = useState({\n    email: '<EMAIL>',\n    password: 'Admin@12345'\n  })\n  const [loading, setLoading] = useState(false)\n  const [result, setResult] = useState<any>(null)\n\n  const handleDirectSignIn = async () => {\n    setLoading(true)\n    setResult(null)\n    \n    try {\n      const result = await signIn('credentials', {\n        email: credentials.email,\n        password: credentials.password,\n        redirect: false\n      })\n      \n      setResult(result)\n    } catch (error) {\n      setResult({ error: error.message })\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const testCredentials = [\n    { email: '<EMAIL>', password: 'Admin@12345', role: 'Admin' },\n    { email: '<EMAIL>', password: 'Teacher@12345', role: 'Teacher' },\n    { email: '<EMAIL>', password: 'Student@12345', role: 'Student' }\n  ]\n\n  return (\n    <div className=\"min-h-screen p-8 bg-gray-50\">\n      <div className=\"max-w-4xl mx-auto space-y-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Login Test Page</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <h3 className=\"font-semibold mb-2\">Current Session Status:</h3>\n              <pre className=\"bg-gray-100 p-3 rounded text-sm\">\n                Status: {status}\n                {session && (\n                  <>\n                    <br />User: {JSON.stringify(session.user, null, 2)}\n                  </>\n                )}\n              </pre>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold mb-2\">Test Direct SignIn:</h3>\n              <div className=\"space-y-2\">\n                <Input\n                  placeholder=\"Email\"\n                  value={credentials.email}\n                  onChange={(e) => setCredentials(prev => ({ ...prev, email: e.target.value }))}\n                />\n                <Input\n                  type=\"password\"\n                  placeholder=\"Password\"\n                  value={credentials.password}\n                  onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}\n                />\n                <Button onClick={handleDirectSignIn} disabled={loading}>\n                  {loading ? 'Testing...' : 'Test SignIn'}\n                </Button>\n              </div>\n              \n              {result && (\n                <div className=\"mt-4\">\n                  <h4 className=\"font-semibold\">SignIn Result:</h4>\n                  <pre className=\"bg-gray-100 p-3 rounded text-sm\">\n                    {JSON.stringify(result, null, 2)}\n                  </pre>\n                </div>\n              )}\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold mb-2\">Quick Test Buttons:</h3>\n              <div className=\"space-y-2\">\n                {testCredentials.map((cred, index) => (\n                  <Button\n                    key={index}\n                    variant=\"outline\"\n                    onClick={() => {\n                      setCredentials({ email: cred.email, password: cred.password })\n                      setTimeout(() => handleDirectSignIn(), 100)\n                    }}\n                    disabled={loading}\n                  >\n                    Test {cred.role} Login\n                  </Button>\n                ))}\n              </div>\n            </div>\n\n            <div>\n              <h3 className=\"font-semibold mb-2\">Navigation Links:</h3>\n              <div className=\"space-x-2\">\n                <Button variant=\"outline\" onClick={() => window.location.href = '/login'}>\n                  Go to Login Page\n                </Button>\n                <Button variant=\"outline\" onClick={() => window.location.href = '/admin'}>\n                  Go to Admin Dashboard\n                </Button>\n                <Button variant=\"outline\" onClick={() => window.location.href = '/teacher'}>\n                  Go to Teacher Dashboard\n                </Button>\n                <Button variant=\"outline\" onClick={() => window.location.href = '/student'}>\n                  Go to Student Dashboard\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,IAAA,6SAAU;IAC5C,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0TAAQ,EAAC;QAC7C,OAAO;QACP,UAAU;IACZ;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,0TAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,0TAAQ,EAAM;IAE1C,MAAM,qBAAqB;QACzB,WAAW;QACX,UAAU;QAEV,IAAI;YACF,MAAM,SAAS,MAAM,IAAA,ySAAM,EAAC,eAAe;gBACzC,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;gBAC9B,UAAU;YACZ;YAEA,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,UAAU;gBAAE,OAAO,MAAM,OAAO;YAAC;QACnC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAqB,UAAU;YAAe,MAAM;QAAQ;QACrE;YAAE,OAAO;YAAwB,UAAU;YAAiB,MAAM;QAAU;QAC5E;YAAE,OAAO;YAAwB,UAAU;YAAiB,MAAM;QAAU;KAC7E;IAED,qBACE,8UAAC;QAAI,WAAU;kBACb,cAAA,8UAAC;YAAI,WAAU;sBACb,cAAA,8UAAC,6KAAI;;kCACH,8UAAC,mLAAU;kCACT,cAAA,8UAAC,kLAAS;sCAAC;;;;;;;;;;;kCAEb,8UAAC,oLAAW;wBAAC,WAAU;;0CACrB,8UAAC;;kDACC,8UAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8UAAC;wCAAI,WAAU;;4CAAkC;4CACtC;4CACR,yBACC;;kEACE,8UAAC;;;;;oDAAK;oDAAO,KAAK,SAAS,CAAC,QAAQ,IAAI,EAAE,MAAM;;;;;;;;;;;;;;;0CAMxD,8UAAC;;kDACC,8UAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8UAAC;wCAAI,WAAU;;0DACb,8UAAC,+KAAK;gDACJ,aAAY;gDACZ,OAAO,YAAY,KAAK;gDACxB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;;;;;;0DAE7E,8UAAC,+KAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO,YAAY,QAAQ;gDAC3B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;;;;;;0DAEhF,8UAAC,iLAAM;gDAAC,SAAS;gDAAoB,UAAU;0DAC5C,UAAU,eAAe;;;;;;;;;;;;oCAI7B,wBACC,8UAAC;wCAAI,WAAU;;0DACb,8UAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8UAAC;gDAAI,WAAU;0DACZ,KAAK,SAAS,CAAC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;0CAMtC,8UAAC;;kDACC,8UAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8UAAC;wCAAI,WAAU;kDACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8UAAC,iLAAM;gDAEL,SAAQ;gDACR,SAAS;oDACP,eAAe;wDAAE,OAAO,KAAK,KAAK;wDAAE,UAAU,KAAK,QAAQ;oDAAC;oDAC5D,WAAW,IAAM,sBAAsB;gDACzC;gDACA,UAAU;;oDACX;oDACO,KAAK,IAAI;oDAAC;;+CARX;;;;;;;;;;;;;;;;0CAcb,8UAAC;;kDACC,8UAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8UAAC;wCAAI,WAAU;;0DACb,8UAAC,iLAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;0DAAU;;;;;;0DAG1E,8UAAC,iLAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;0DAAU;;;;;;0DAG1E,8UAAC,iLAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;0DAAY;;;;;;0DAG5E,8UAAC,iLAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5F;GA5HwB;;QACY,6SAAU;;;KADtB", "debugId": null}}]}