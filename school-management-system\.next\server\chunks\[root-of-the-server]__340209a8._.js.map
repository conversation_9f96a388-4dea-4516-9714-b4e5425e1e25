{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\r\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\r\nimport bcrypt from 'bcryptjs'\r\nimport { prisma } from './db'\r\n\r\nexport const authOptions: NextAuthOptions = {\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: 'credentials',\r\n      credentials: {\r\n        email: { label: 'Email', type: 'email' },\r\n        password: { label: 'Password', type: 'password' }\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials?.email || !credentials?.password) {\r\n          return null\r\n        }\r\n\r\n        try {\r\n          const user = await prisma.user.findUnique({\r\n            where: {\r\n              email: credentials.email\r\n            }\r\n          })\r\n\r\n          if (!user || !user.hashedPassword) {\r\n            return null\r\n          }\r\n\r\n          const isCorrectPassword = await bcrypt.compare(\r\n            credentials.password,\r\n            user.hashedPassword\r\n          )\r\n\r\n          if (!isCorrectPassword) {\r\n            return null\r\n          }\r\n\r\n          return {\r\n            id: user.id,\r\n            email: user.email,\r\n            name: `${user.firstName} ${user.lastName}`,\r\n            role: user.role,\r\n            firstName: user.firstName,\r\n            lastName: user.lastName\r\n          }\r\n        } catch (error) {\r\n          console.error('Auth error:', error)\r\n          return null\r\n        }\r\n      }\r\n    })\r\n  ],\r\n  session: {\r\n    strategy: 'jwt',\r\n    maxAge: 24 * 60 * 60, // 24 hours\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.role = user.role\r\n        token.firstName = user.firstName\r\n        token.lastName = user.lastName\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token) {\r\n        session.user.id = token.sub!\r\n        session.user.role = token.role as string\r\n        session.user.firstName = token.firstName as string\r\n        session.user.lastName = token.lastName as string\r\n      }\r\n      return session\r\n    }\r\n  },\r\n  pages: {\r\n    signIn: '/login',\r\n    error: '/login'\r\n  },\r\n  secret: process.env.NEXTAUTH_SECRET\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,IAAA,mTAAmB,EAAC;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,OAAO;4BACL,OAAO,YAAY,KAAK;wBAC1B;oBACF;oBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;wBACjC,OAAO;oBACT;oBAEA,MAAM,oBAAoB,MAAM,qOAAM,CAAC,OAAO,CAC5C,YAAY,QAAQ,EACpB,KAAK,cAAc;oBAGrB,IAAI,CAAC,mBAAmB;wBACtB,OAAO;oBACT;oBAEA,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,MAAM,KAAK,IAAI;wBACf,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;oBACzB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/rbac.ts"], "sourcesContent": ["import { UserRole } from '@prisma/client'\r\n\r\n// Define permission types\r\nexport type Permission = \r\n  | 'users:read' | 'users:write' | 'users:delete'\r\n  | 'students:read' | 'students:write' | 'students:delete'\r\n  | 'teachers:read' | 'teachers:write' | 'teachers:delete'\r\n  | 'classes:read' | 'classes:write' | 'classes:delete'\r\n  | 'subjects:read' | 'subjects:write' | 'subjects:delete'\r\n  | 'attendance:read' | 'attendance:write'\r\n  | 'marks:read' | 'marks:write'\r\n  | 'reports:read' | 'reports:write'\r\n  | 'settings:read' | 'settings:write'\r\n  | 'audit:read'\r\n\r\n// Define role permissions\r\nconst rolePermissions: Record<UserRole, Permission[]> = {\r\n  ADMIN: [\r\n    'users:read', 'users:write', 'users:delete',\r\n    'students:read', 'students:write', 'students:delete',\r\n    'teachers:read', 'teachers:write', 'teachers:delete',\r\n    'classes:read', 'classes:write', 'classes:delete',\r\n    'subjects:read', 'subjects:write', 'subjects:delete',\r\n    'attendance:read', 'attendance:write',\r\n    'marks:read', 'marks:write',\r\n    'reports:read', 'reports:write',\r\n    'settings:read', 'settings:write',\r\n    'audit:read'\r\n  ],\r\n  TEACHER: [\r\n    'students:read',\r\n    'attendance:read', 'attendance:write',\r\n    'marks:read', 'marks:write',\r\n    'reports:read'\r\n  ],\r\n  STUDENT: [\r\n    'attendance:read',\r\n    'marks:read',\r\n    'reports:read'\r\n  ]\r\n}\r\n\r\n/**\r\n * Check if a user has a specific permission\r\n */\r\nexport function hasPermission(userRole: UserRole | string, permission: Permission): boolean {\r\n  const role = userRole as UserRole;\r\n  return rolePermissions[role]?.includes(permission) ?? false\r\n}\r\n\r\n/**\r\n * Check if a user can access a specific resource\r\n */\r\nexport function canAccess(userRole: UserRole | string, resource: string, action: 'read' | 'write' | 'delete'): boolean {\r\n  const permission = `${resource}:${action}` as Permission\r\n  return hasPermission(userRole, permission)\r\n}\r\n\r\n/**\r\n * Get all permissions for a role\r\n */\r\nexport function getRolePermissions(role: UserRole | string): Permission[] {\r\n  const userRole = role as UserRole;\r\n  return rolePermissions[userRole] ?? []\r\n}\r\n\r\n/**\r\n * Check if user can access student data (teachers can only see their assigned students)\r\n */\r\nexport function canAccessStudentData(userRole: UserRole | string, teacherId?: string, studentClassId?: string): boolean {\r\n  const role = userRole as UserRole;\r\n  if (role === 'ADMIN') return true\r\n  if (role === 'STUDENT') return false // Students can't access other students' data\r\n  \r\n  // For teachers, we'll need additional logic to check if they're assigned to the student's class\r\n  // This will be implemented when we add teacher-class assignments\r\n  return role === 'TEACHER'\r\n}\r\n\r\n/**\r\n * Check if user can access class data (teachers can only see their assigned classes)\r\n */\r\nexport function canAccessClassData(userRole: UserRole | string, teacherId?: string, classId?: string): boolean {\r\n  const role = userRole as UserRole;\r\n  if (role === 'ADMIN') return true\r\n  \r\n  // For teachers, we'll need additional logic to check if they're assigned to the class\r\n  // This will be implemented when we add teacher-class assignments\r\n  return role === 'TEACHER'\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAeA,0BAA0B;AAC1B,MAAM,kBAAkD;IACtD,OAAO;QACL;QAAc;QAAe;QAC7B;QAAiB;QAAkB;QACnC;QAAiB;QAAkB;QACnC;QAAgB;QAAiB;QACjC;QAAiB;QAAkB;QACnC;QAAmB;QACnB;QAAc;QACd;QAAgB;QAChB;QAAiB;QACjB;KACD;IACD,SAAS;QACP;QACA;QAAmB;QACnB;QAAc;QACd;KACD;IACD,SAAS;QACP;QACA;QACA;KACD;AACH;AAKO,SAAS,cAAc,QAA2B,EAAE,UAAsB;IAC/E,MAAM,OAAO;IACb,OAAO,eAAe,CAAC,KAAK,EAAE,SAAS,eAAe;AACxD;AAKO,SAAS,UAAU,QAA2B,EAAE,QAAgB,EAAE,MAAmC;IAC1G,MAAM,aAAa,GAAG,SAAS,CAAC,EAAE,QAAQ;IAC1C,OAAO,cAAc,UAAU;AACjC;AAKO,SAAS,mBAAmB,IAAuB;IACxD,MAAM,WAAW;IACjB,OAAO,eAAe,CAAC,SAAS,IAAI,EAAE;AACxC;AAKO,SAAS,qBAAqB,QAA2B,EAAE,SAAkB,EAAE,cAAuB;IAC3G,MAAM,OAAO;IACb,IAAI,SAAS,SAAS,OAAO;IAC7B,IAAI,SAAS,WAAW,OAAO,MAAM,6CAA6C;;IAElF,gGAAgG;IAChG,iEAAiE;IACjE,OAAO,SAAS;AAClB;AAKO,SAAS,mBAAmB,QAA2B,EAAE,SAAkB,EAAE,OAAgB;IAClG,MAAM,OAAO;IACb,IAAI,SAAS,SAAS,OAAO;IAE7B,sFAAsF;IACtF,iEAAiE;IACjE,OAAO,SAAS;AAClB", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/api/admin/teachers/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { getServerSession } from 'next-auth';\r\nimport { authOptions } from '@/lib/auth';\r\nimport { prisma } from '@/lib/db';\r\nimport { hasPermission } from '@/lib/rbac';\r\n\r\n// GET /api/admin/teachers/[id] - Get specific teacher\r\nexport async function GET(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const session = await getServerSession(authOptions);\r\n    if (!session?.user || !hasPermission(session.user.role, 'teachers:read')) {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\r\n    }\r\n\r\n    const resolvedParams = await params;\r\n    const teacherId = resolvedParams.id;\r\n\r\n    const teacher = await prisma.teacher.findUnique({\r\n      where: { id: teacherId },\r\n      include: {\r\n        user: {\r\n          select: {\r\n            id: true,\r\n            email: true,\r\n            role: true,\r\n          },\r\n        },\r\n        attendances: true,\r\n        marks: true,\r\n      },\r\n    });\r\n\r\n    if (!teacher) {\r\n      return NextResponse.json(\r\n        { error: 'Teacher not found' },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    return NextResponse.json({ teacher });\r\n  } catch (error) {\r\n    console.error('Error fetching teacher:', error);\r\n    return NextResponse.json(\r\n      { error: 'Failed to fetch teacher' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// PUT /api/admin/teachers/[id] - Update specific teacher\r\nexport async function PUT(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const session = await getServerSession(authOptions);\r\n    if (!session?.user || !hasPermission(session.user.role, 'teachers:write')) {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\r\n    }\r\n\r\n    const resolvedParams = await params;\r\n    const teacherId = resolvedParams.id;\r\n\r\n    const body = await request.json();\r\n\r\n    // Check if teacher exists\r\n    const existingTeacher = await prisma.teacher.findUnique({\r\n      where: { id: teacherId },\r\n    });\r\n\r\n    if (!existingTeacher) {\r\n      return NextResponse.json(\r\n        { error: 'Teacher not found' },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // Update teacher\r\n    const updatedTeacher = await prisma.teacher.update({\r\n      where: { id: teacherId },\r\n      data: body,\r\n      include: {\r\n        user: {\r\n          select: {\r\n            id: true,\r\n            email: true,\r\n            role: true,\r\n          },\r\n        },\r\n        attendances: true,\r\n        marks: true,\r\n      },\r\n    });\r\n\r\n    return NextResponse.json({\r\n      message: 'Teacher updated successfully',\r\n      teacher: updatedTeacher,\r\n    });\r\n  } catch (error) {\r\n    console.error('Error updating teacher:', error);\r\n    return NextResponse.json(\r\n      { error: 'Failed to update teacher' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// DELETE /api/admin/teachers/[id] - Delete teacher\r\nexport async function DELETE(\r\n  request: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  try {\r\n    const session = await getServerSession(authOptions);\r\n    if (!session?.user || !hasPermission(session.user.role, 'teachers:delete')) {\r\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });\r\n    }\r\n\r\n    const resolvedParams = await params;\r\n    const teacherId = resolvedParams.id;\r\n\r\n    // Check if teacher exists\r\n    const existingTeacher = await prisma.teacher.findUnique({\r\n      where: { id: teacherId },\r\n      include: {\r\n        user: true,\r\n        attendances: true,\r\n        marks: true,\r\n      },\r\n    });\r\n\r\n    if (!existingTeacher) {\r\n      return NextResponse.json(\r\n        { error: 'Teacher not found' },\r\n        { status: 404 }\r\n      );\r\n    }\r\n\r\n    // Check if teacher has active attendances or marks\r\n    if (existingTeacher.attendances.length > 0 || existingTeacher.marks.length > 0) {\r\n      return NextResponse.json(\r\n        {\r\n          error: 'Cannot delete teacher with active records. Please reassign or deactivate instead.'\r\n        },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Delete teacher and associated user account\r\n    await prisma.$transaction([\r\n      prisma.teacher.delete({\r\n        where: { id: teacherId },\r\n      }),\r\n      prisma.user.delete({\r\n        where: { id: existingTeacher.user.id },\r\n      }),\r\n    ]);\r\n\r\n    return NextResponse.json({\r\n      message: 'Teacher deleted successfully',\r\n    });\r\n  } catch (error) {\r\n    console.error('Error deleting teacher:', error);\r\n    return NextResponse.json(\r\n      { error: 'Failed to delete teacher' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAClD,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAA,uKAAa,EAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,kBAAkB;YACxE,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,iBAAiB,MAAM;QAC7B,MAAM,YAAY,eAAe,EAAE;QAEnC,MAAM,UAAU,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE,IAAI;YAAU;YACvB,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,MAAM;oBACR;gBACF;gBACA,aAAa;gBACb,OAAO;YACT;QACF;QAEA,IAAI,CAAC,SAAS;YACZ,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,iSAAY,CAAC,IAAI,CAAC;YAAE;QAAQ;IACrC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,iSAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAClD,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAA,uKAAa,EAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,mBAAmB;YACzE,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,iBAAiB,MAAM;QAC7B,MAAM,YAAY,eAAe,EAAE;QAEnC,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,0BAA0B;QAC1B,MAAM,kBAAkB,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACtD,OAAO;gBAAE,IAAI;YAAU;QACzB;QAEA,IAAI,CAAC,iBAAiB;YACpB,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,iBAAiB,MAAM,8JAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YACjD,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;YACN,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,MAAM;oBACR;gBACF;gBACA,aAAa;gBACb,OAAO;YACT;QACF;QAEA,OAAO,iSAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,iSAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAClD,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAA,uKAAa,EAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,oBAAoB;YAC1E,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,iBAAiB,MAAM;QAC7B,MAAM,YAAY,eAAe,EAAE;QAEnC,0BAA0B;QAC1B,MAAM,kBAAkB,MAAM,8JAAM,CAAC,OAAO,CAAC,UAAU,CAAC;YACtD,OAAO;gBAAE,IAAI;YAAU;YACvB,SAAS;gBACP,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;QACF;QAEA,IAAI,CAAC,iBAAiB;YACpB,OAAO,iSAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,mDAAmD;QACnD,IAAI,gBAAgB,WAAW,CAAC,MAAM,GAAG,KAAK,gBAAgB,KAAK,CAAC,MAAM,GAAG,GAAG;YAC9E,OAAO,iSAAY,CAAC,IAAI,CACtB;gBACE,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,6CAA6C;QAC7C,MAAM,8JAAM,CAAC,YAAY,CAAC;YACxB,8JAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gBACpB,OAAO;oBAAE,IAAI;gBAAU;YACzB;YACA,8JAAM,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjB,OAAO;oBAAE,IAAI,gBAAgB,IAAI,CAAC,EAAE;gBAAC;YACvC;SACD;QAED,OAAO,iSAAY,CAAC,IAAI,CAAC;YACvB,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,iSAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}