# NextAuth Configuration
NEXTAUTH_SECRET=c2Nob29sbWFuYWdlbWVudHNlY3JldGtleTIwMjR2ZXJ5bG9uZ2FuZHNlY3VyZQ==
NEXTAUTH_URL=http://localhost:3001

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://bdnidaohrfvxnepdbrfg.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJkbmlkYW9ocmZ2eG5lcGRicmZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY4MTI3NjksImV4cCI6MjA3MjM4ODc2OX0.BTJgpatNLqUOs4VzTEvWHp1xS4xPbZilXjWfIxl6_l4

# Database URL (if using SQLite, this should point to the prisma/dev.db file)
DATABASE_URL="file:./dev.db"

# Email configuration (optional - for password reset)
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
